#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V6代码完整性测试脚本
用于验证v6文件夹中所有代码的完整性和可靠性
"""

import sys
import os
import traceback
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_imports():
    """测试所有导入是否正常"""
    print("="*50)
    print("1. 导入测试")
    print("="*50)
    
    import_tests = [
        ("主模型", "from model import KongwenGonglvCorrectionModel"),
        ("副功率预测器", "from production_deployment.src.predict import VicePowerPredictor"),
        ("改进预测器", "from production_deployment.src.predict_improved import VicePowerPredictor as ImprovedPredictor"),
        ("改进模型", "from production_deployment.src.improved_vice_power_model import ImprovedVicePowerModel"),
        ("高功率适配器", "from production_deployment.models.high_power_model.optimized_adapter import OptimizedHighPowerAdapter"),
    ]
    
    results = {}
    for name, import_stmt in import_tests:
        try:
            exec(import_stmt)
            print(f"✅ {name}: 导入成功")
            results[name] = True
        except Exception as e:
            print(f"❌ {name}: 导入失败 - {e}")
            results[name] = False
    
    return results

def test_model_initialization():
    """测试模型初始化"""
    print("\n" + "="*50)
    print("2. 模型初始化测试")
    print("="*50)
    
    try:
        from model import KongwenGonglvCorrectionModel
        
        # 测试配置文件加载
        config_path = "model_data/config.yaml"
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            return False
        
        model = KongwenGonglvCorrectionModel.from_path(config_path)
        print("✅ 主模型初始化成功")
        
        # 测试模型设置
        model.setup(
            device_id='A37',
            jialiao=200,
            times=1,
            power_yinjing=65,
            init_power=[100, 80],
            config={},
            field_size=36,
            product_type=11,
            target_ccd=1448,
            history_data=[],
            feeding_type=1
        )
        print("✅ 模型设置成功")
        print(f"  副功率预测器状态: {'可用' if model.vice_power_predictor else '不可用'}")
        
        return model
        
    except Exception as e:
        print(f"❌ 模型初始化失败: {e}")
        traceback.print_exc()
        return None

def test_prediction_functionality(model):
    """测试预测功能"""
    print("\n" + "="*50)
    print("3. 预测功能测试")
    print("="*50)
    
    if model is None:
        print("❌ 模型未初始化，跳过预测测试")
        return False
    
    try:
        # 测试预测
        result = model.predict(
            t=600,
            ratio=35,
            ccd=1445,
            ccd3=1450,
            fullmelting=0,
            sum_jialiao_time=300,
            last_jialiao_time=60,
            last_jialiao_weight=25,
            last_Interval_time=120,
            barrelage=8,
            last_but_one_jialiao_weight=30,
            last_but_one_jialiao_time=55,
            last_but_one_jialiao_interval_time=110,
            film_ratio=15,
            turnover_ratio=25,
            time_interval=60.0,
            cumulative_feed_weight=175.0
        )
        
        print("✅ 预测执行成功")
        
        if isinstance(result, tuple) and len(result) == 3:
            main_power, vice_power, vice_power_info = result
            print(f"  主功率: {main_power} kW")
            print(f"  副功率: {vice_power} kW")
            
            if isinstance(vice_power_info, list) and len(vice_power_info) == 3:
                real_time_vice, cumulative_vice, predicted_total = vice_power_info
                print(f"  实时副功率: {real_time_vice} kW")
                print(f"  累积副功率: {cumulative_vice:.2f} kWh")
                print(f"  预测总量: {predicted_total} kWh")
                
                if predicted_total is not None:
                    print("✅ 副功率预测器工作正常")
                    return True
                else:
                    print("⚠️ 副功率预测器返回None")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ 预测功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_vice_power_predictor():
    """测试副功率预测器"""
    print("\n" + "="*50)
    print("4. 副功率预测器独立测试")
    print("="*50)
    
    try:
        from production_deployment.src.predict import VicePowerPredictor
        
        models_dir = "production_deployment/models"
        predictor = VicePowerPredictor(models_dir=models_dir)
        print("✅ 副功率预测器初始化成功")
        
        # 测试预测
        result = predictor.predict_single(
            weight_difference=150,
            silicon_thermal_energy_kwh=120,
            process_type='复投'
        )
        
        print("✅ 副功率预测器预测成功")
        print(f"  预测结果: {result}")
        
        if result.get('predicted_vice_power_kwh') is not None:
            print("✅ 副功率预测器返回有效值")
            return True
        else:
            print("❌ 副功率预测器返回无效值")
            return False
        
    except Exception as e:
        print(f"❌ 副功率预测器测试失败: {e}")
        traceback.print_exc()
        return False

def test_improved_model():
    """测试改进模型"""
    print("\n" + "="*50)
    print("5. 改进模型独立测试")
    print("="*50)
    
    try:
        from production_deployment.src.improved_vice_power_model import ImprovedVicePowerModel
        
        model = ImprovedVicePowerModel()
        print("✅ 改进模型初始化成功")
        
        # 测试模型加载
        model_path = "production_deployment/models/improved_vice_power_model.joblib"
        success = model.load_model(model_path)
        
        if success:
            print("✅ 改进模型加载成功")
        else:
            print("⚠️ 改进模型加载失败，使用默认参数")
        
        # 测试预测
        import pandas as pd
        import numpy as np
        
        X = pd.DataFrame({
            'feature_1': [150.0],
            'feature_2': [120.0],
            'feature_3': [100.0],
            'feature_4': [75.0],
            'feature_5': [50.0]
        })
        
        power_values = np.array([150.0])
        predictions = model.predict(X, power_values)
        
        print("✅ 改进模型预测成功")
        print(f"  预测结果: {predictions}")
        
        return True
        
    except Exception as e:
        print(f"❌ 改进模型测试失败: {e}")
        traceback.print_exc()
        return False

def test_file_integrity():
    """测试文件完整性"""
    print("\n" + "="*50)
    print("6. 文件完整性检查")
    print("="*50)
    
    required_files = [
        "model.py",
        "call.py",
        "model_data/config.yaml",
        "production_deployment/src/__init__.py",
        "production_deployment/src/predict.py",
        "production_deployment/src/predict_improved.py",
        "production_deployment/src/improved_vice_power_model.py",
        "production_deployment/models/production_models.joblib",
        "production_deployment/models/high_power_model/optimized_adapter.py",
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 文件缺失")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ 发现 {len(missing_files)} 个缺失文件")
        return False
    else:
        print("\n✅ 所有必需文件都存在")
        return True

def main():
    """主测试函数"""
    print("V6代码完整性测试")
    print("="*60)
    
    # 切换到v6目录
    os.chdir(Path(__file__).parent)
    
    test_results = {}
    
    # 1. 导入测试
    test_results['imports'] = test_imports()
    
    # 2. 文件完整性检查
    test_results['file_integrity'] = test_file_integrity()
    
    # 3. 模型初始化测试
    model = test_model_initialization()
    test_results['model_init'] = model is not None
    
    # 4. 预测功能测试
    test_results['prediction'] = test_prediction_functionality(model)
    
    # 5. 副功率预测器测试
    test_results['vice_predictor'] = test_vice_power_predictor()
    
    # 6. 改进模型测试
    test_results['improved_model'] = test_improved_model()
    
    # 总结
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results.values() if result)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！V6代码已准备好用于现场部署。")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步修复。")
        return False

if __name__ == "__main__":
    main()
