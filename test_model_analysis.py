#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
KongwenGonglvCorrectionModel 深度分析测试脚本
"""

import sys
import os
import numpy as np
import pandas as pd
import traceback
from datetime import datetime
from pathlib import Path

# 添加项目路径到系统路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_environment():
    """检查当前环境"""
    import subprocess
    try:
        result = subprocess.run(['conda', 'info', '--envs'], capture_output=True, text=True)
        current_env = None
        for line in result.stdout.split('\n'):
            if '*' in line:
                current_env = line.split()[0]
                break
        
        print(f"当前环境: {current_env}")
        if current_env != 'lj_env_1':
            print("⚠️ 警告：当前环境不是 lj_env_1")
            print("请运行: conda activate lj_env_1")
            return False
        else:
            print("✅ 环境检查通过")
            return True
    except Exception as e:
        print(f"环境检查失败: {e}")
        return False

def test_model_import():
    """测试模型导入"""
    print("\n" + "="*50)
    print("1. 模型导入测试")
    print("="*50)
    
    try:
        from kongwen_power_control.beta_version.v6.model import KongwenGonglvCorrectionModel
        print("✅ 主模型导入成功")
        
        # 检查副功率预测器导入状态
        from kongwen_power_control.beta_version.v6.model import VICE_POWER_PREDICTOR_AVAILABLE
        print(f"副功率预测器可用状态: {VICE_POWER_PREDICTOR_AVAILABLE}")
        
        return KongwenGonglvCorrectionModel, True
    except Exception as e:
        print(f"❌ 模型导入失败: {e}")
        traceback.print_exc()
        return None, False

def analyze_model_architecture(model_class):
    """分析模型架构"""
    print("\n" + "="*50)
    print("2. 模型架构分析")
    print("="*50)
    
    # 分析类结构
    print("2.1 类常量定义:")
    phases = {
        'INIT': model_class.INIT,
        'VICE_CLOSE1': model_class.VICE_CLOSE1,
        'VICE_CLOSE2': model_class.VICE_CLOSE2,
        'VICE_REOPEN': model_class.VICE_REOPEN,
        'ALMOST_DONE': model_class.ALMOST_DONE,
        'DONE': model_class.DONE
    }
    for name, value in phases.items():
        print(f"  {name}: {value}")
    
    # 分析主要方法
    print("\n2.2 主要方法:")
    methods = [method for method in dir(model_class) if not method.startswith('_')]
    for method in methods:
        if callable(getattr(model_class, method)):
            print(f"  - {method}")
    
    # 分析核心算法
    print("\n2.3 核心算法组件:")
    core_components = [
        'predict',  # 主预测方法
        'setup',    # 初始化设置
        'find_close_time_range',  # 历史数据匹配
        'control_vice_power',     # 副功率控制
        '_predict_vice_power_realtime',  # 实时副功率预测
        '_calculate_real_time_vice_power',  # 实时副功率计算
        'calculate_silicon_thermal_energy'  # 硅热能计算
    ]
    
    for component in core_components:
        if hasattr(model_class, component):
            method = getattr(model_class, component)
            if callable(method):
                print(f"  ✅ {component}: 可用")
            else:
                print(f"  ❌ {component}: 不可调用")
        else:
            print(f"  ❌ {component}: 不存在")

def test_model_initialization():
    """测试模型初始化"""
    print("\n" + "="*50)
    print("3. 模型初始化测试")
    print("="*50)
    
    try:
        from kongwen_power_control.beta_version.v6.model import KongwenGonglvCorrectionModel
        
        # 测试配置文件加载
        config_path = "kongwen_power_control/beta_version/v6/model_data/config.yaml"
        print(f"配置文件路径: {config_path}")
        
        if not os.path.exists(config_path):
            print(f"❌ 配置文件不存在: {config_path}")
            return None
        
        model = KongwenGonglvCorrectionModel.from_path(config_path)
        print("✅ 模型初始化成功")
        
        # 检查配置参数
        print("\n3.1 配置参数:")
        config_attrs = [
            'vice_close_ratio', 'high_ratio', 'time_range', 'init_power',
            'done_power_k', 'begin_t', 'undo_main_ratio', 'done_ratio'
        ]
        
        for attr in config_attrs:
            if hasattr(model, attr):
                value = getattr(model, attr)
                print(f"  {attr}: {value}")
            else:
                print(f"  ❌ {attr}: 未找到")
        
        return model
    except Exception as e:
        print(f"❌ 模型初始化失败: {e}")
        traceback.print_exc()
        return None

def create_test_data():
    """创建测试数据"""
    print("\n" + "="*50)
    print("4. 创建测试数据")
    print("="*50)
    
    # 模拟真实的晶体拉制过程数据
    test_data = {
        # 基本参数
        'device_id': 'A37',
        'jialiao': 200,  # 总加料量 (kg)
        'times': 1,
        'power_yinjing': 65,  # 引晶功率
        'init_power': [100, 80],  # [主功率, 副功率]
        'config': {},
        'field_size': 36,
        'product_type': 11,
        'target_ccd': 1448,
        'history_data': [],  # 历史数据
        'feeding_type': 1,   # 复投
        
        # 实时数据
        't': 600,  # 时间 (秒)
        'ratio': 35,  # 溶液比
        'ccd': 1445,  # CCD温度
        'ccd3': 1450,  # CCD3温度
        'fullmelting': 0,  # 全熔状态
        'sum_jialiao_time': 300,  # 总加料时间
        'last_jialiao_time': 60,   # 最后一次加料时间
        'last_jialiao_weight': 25,  # 最后一次加料重量
        'last_Interval_time': 120,  # 最后一次加料间隔
        'barrelage': 8,  # 加料桶数
        'last_but_one_jialiao_weight': 30,
        'last_but_one_jialiao_time': 55,
        'last_but_one_jialiao_interval_time': 110,
        'film_ratio': 15,  # 薄膜占比
        'turnover_ratio': 25,  # 翻料占比
        'time_interval': 60.0,  # 时间间隔
        'cumulative_feed_weight': 175.0  # 累积加料重量
    }
    
    print("✅ 测试数据创建完成")
    print(f"设备ID: {test_data['device_id']}")
    print(f"总加料量: {test_data['jialiao']} kg")
    print(f"引晶功率: {test_data['power_yinjing']} kW")
    print(f"当前溶液比: {test_data['ratio']}%")
    print(f"CCD温度: {test_data['ccd']}°C")
    
    return test_data

def test_model_setup(model, test_data):
    """测试模型设置"""
    print("\n" + "="*50)
    print("5. 模型设置测试")
    print("="*50)
    
    try:
        model.setup(
            device_id=test_data['device_id'],
            jialiao=test_data['jialiao'],
            times=test_data['times'],
            power_yinjing=test_data['power_yinjing'],
            init_power=test_data['init_power'],
            config=test_data['config'],
            field_size=test_data['field_size'],
            product_type=test_data['product_type'],
            target_ccd=test_data['target_ccd'],
            history_data=test_data['history_data'],
            feeding_type=test_data['feeding_type']
        )
        
        print("✅ 模型设置成功")
        
        # 检查设置后的状态
        print("\n5.1 设置后的模型状态:")
        print(f"  当前阶段: {model.phase}")
        print(f"  当前主功率: {model.cur_main_power}")
        print(f"  当前副功率: {model.cur_vice_power}")
        print(f"  副功率预测器: {'可用' if model.vice_power_predictor else '不可用'}")
        
        return True
    except Exception as e:
        print(f"❌ 模型设置失败: {e}")
        traceback.print_exc()
        return False

def test_prediction_functionality(model, test_data):
    """测试预测功能"""
    print("\n" + "="*50)
    print("6. 预测功能测试")
    print("="*50)

    try:
        # 执行预测
        result = model.predict(
            t=test_data['t'],
            ratio=test_data['ratio'],
            ccd=test_data['ccd'],
            ccd3=test_data['ccd3'],
            fullmelting=test_data['fullmelting'],
            sum_jialiao_time=test_data['sum_jialiao_time'],
            last_jialiao_time=test_data['last_jialiao_time'],
            last_jialiao_weight=test_data['last_jialiao_weight'],
            last_Interval_time=test_data['last_Interval_time'],
            barrelage=test_data['barrelage'],
            last_but_one_jialiao_weight=test_data['last_but_one_jialiao_weight'],
            last_but_one_jialiao_time=test_data['last_but_one_jialiao_time'],
            last_but_one_jialiao_interval_time=test_data['last_but_one_jialiao_interval_time'],
            film_ratio=test_data['film_ratio'],
            turnover_ratio=test_data['turnover_ratio'],
            time_interval=test_data['time_interval'],
            cumulative_feed_weight=test_data['cumulative_feed_weight']
        )

        print("✅ 预测执行成功")

        # 分析预测结果
        print("\n6.1 预测结果分析:")
        if isinstance(result, tuple) and len(result) == 3:
            main_power, vice_power, vice_power_info = result
            print(f"  主功率: {main_power} kW")
            print(f"  副功率: {vice_power} kW")
            print(f"  副功率信息: {vice_power_info}")

            if isinstance(vice_power_info, list) and len(vice_power_info) == 3:
                real_time_vice, cumulative_vice, predicted_total = vice_power_info
                print(f"    实时副功率: {real_time_vice} kW")
                print(f"    累积副功率: {cumulative_vice} kWh")
                print(f"    预测总量: {predicted_total} kWh")
        else:
            print(f"  预测结果格式: {type(result)}")
            print(f"  预测结果: {result}")

        return result
    except Exception as e:
        print(f"❌ 预测执行失败: {e}")
        traceback.print_exc()
        return None

def test_phase_transitions(model, test_data):
    """测试阶段转换"""
    print("\n" + "="*50)
    print("7. 阶段转换测试")
    print("="*50)

    # 测试不同阶段的转换
    test_scenarios = [
        {
            'name': '初始阶段 (t=5min, ratio=20%)',
            'params': {'t': 300, 'ratio': 20, 'ccd': -1}
        },
        {
            'name': '副功率半关阶段 (t=15min, ratio=40%)',
            'params': {'t': 900, 'ratio': 40, 'ccd': -1}
        },
        {
            'name': '副功率全关阶段 (t=25min, ratio=50%)',
            'params': {'t': 1500, 'ratio': 50, 'ccd': -1}
        },
        {
            'name': '接近全熔阶段 (t=35min, ratio=90%)',
            'params': {'t': 2100, 'ratio': 90, 'ccd': -1}
        },
        {
            'name': '全熔完成阶段 (t=45min, ratio=98%, CCD有效)',
            'params': {'t': 2700, 'ratio': 98, 'ccd': 1446}
        }
    ]

    for i, scenario in enumerate(test_scenarios):
        print(f"\n7.{i+1} {scenario['name']}")

        # 更新测试数据
        updated_data = test_data.copy()
        updated_data.update(scenario['params'])

        try:
            result = model.predict(
                t=updated_data['t'],
                ratio=updated_data['ratio'],
                ccd=updated_data['ccd'],
                ccd3=updated_data['ccd3'],
                fullmelting=updated_data['fullmelting'],
                sum_jialiao_time=updated_data['sum_jialiao_time'],
                last_jialiao_time=updated_data['last_jialiao_time'],
                last_jialiao_weight=updated_data['last_jialiao_weight'],
                last_Interval_time=updated_data['last_Interval_time'],
                barrelage=updated_data['barrelage'],
                last_but_one_jialiao_weight=updated_data['last_but_one_jialiao_weight'],
                last_but_one_jialiao_time=updated_data['last_but_one_jialiao_time'],
                last_but_one_jialiao_interval_time=updated_data['last_but_one_jialiao_interval_time'],
                film_ratio=updated_data['film_ratio'],
                turnover_ratio=updated_data['turnover_ratio'],
                time_interval=updated_data['time_interval'],
                cumulative_feed_weight=updated_data['cumulative_feed_weight']
            )

            main_power, vice_power, vice_power_info = result
            print(f"  阶段: {model.phase} | 主功率: {main_power} kW | 副功率: {vice_power} kW")

        except Exception as e:
            print(f"  ❌ 测试失败: {e}")

def test_thermal_energy_calculation(model):
    """测试硅热能计算"""
    print("\n" + "="*50)
    print("8. 硅热能计算测试")
    print("="*50)

    test_cases = [
        {'weight': 100, 'temp': 1000, 'desc': '100kg硅加热到1000°C'},
        {'weight': 200, 'temp': 1414, 'desc': '200kg硅加热到熔点1414°C'},
        {'weight': 150, 'temp': 1448, 'desc': '150kg硅加热到1448°C（液态）'},
        {'weight': 300, 'temp': 1500, 'desc': '300kg硅加热到1500°C（液态）'}
    ]

    for case in test_cases:
        try:
            energy_j = model.calculate_silicon_thermal_energy(case['weight'], case['temp'])
            energy_kwh = energy_j / 3.6e6  # 转换为kWh
            print(f"  {case['desc']}: {energy_kwh:.2f} kWh ({energy_j:.0f} J)")
        except Exception as e:
            print(f"  ❌ {case['desc']}: 计算失败 - {e}")

def test_vice_power_features(model):
    """测试副功率特征计算"""
    print("\n" + "="*50)
    print("9. 副功率特征计算测试")
    print("="*50)

    test_cases = [
        {'ccd': 1448, 'weight': 150, 'desc': '标准情况'},
        {'ccd': 1400, 'weight': 200, 'desc': '低温大重量'},
        {'ccd': 1500, 'weight': 100, 'desc': '高温小重量'},
        {'ccd': 0, 'weight': 175, 'desc': 'CCD无效值'}
    ]

    for case in test_cases:
        try:
            features = model._calculate_vice_power_features(
                ccd_temperature=case['ccd'],
                cumulative_feed_weight=case['weight']
            )
            print(f"  {case['desc']}:")
            print(f"    重量差异: {features['weight_difference']:.1f} kg")
            print(f"    硅热能: {features['silicon_thermal_energy_kwh']:.1f} kWh")
        except Exception as e:
            print(f"  ❌ {case['desc']}: 计算失败 - {e}")

def main():
    """主函数"""
    print("KongwenGonglvCorrectionModel 深度分析测试")
    print("="*60)

    # 1. 环境检查
    env_ok = check_environment()
    if not env_ok:
        print("⚠️ 环境不是 lj_env_1，但继续测试...")
        print("如果遇到依赖问题，请切换到 lj_env_1 环境")
    
    # 2. 模型导入测试
    model_class, import_success = test_model_import()
    if not import_success:
        return
    
    # 3. 模型架构分析
    analyze_model_architecture(model_class)
    
    # 4. 模型初始化测试
    model = test_model_initialization()
    if model is None:
        return
    
    # 5. 创建测试数据
    test_data = create_test_data()
    
    # 6. 模型设置测试
    setup_success = test_model_setup(model, test_data)
    if not setup_success:
        return
    
    # 7. 预测功能测试
    prediction_result = test_prediction_functionality(model, test_data)

    # 8. 阶段转换测试
    test_phase_transitions(model, test_data)

    # 9. 硅热能计算测试
    test_thermal_energy_calculation(model)

    # 10. 副功率特征计算测试
    test_vice_power_features(model)

    print("\n" + "="*60)
    print("测试完成")
    print("="*60)

if __name__ == "__main__":
    main()
